import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import qrcode
from PIL import Image, ImageTk
import os
from datetime import datetime
import subprocess  # For share_qr
from typing import Optional, Dict, Any
from pyzbar.pyzbar import decode


class QRCodeGenerator:
    def __init__(self, root: tk.Tk) -> None:
        self.root = root
        self.root.title("QR Code Generator & Decoder")
        self.root.minsize(450, 520)
        self.root.overrideredirect(True)
        
        # Variables
        self.qr_image_pil: Optional[Image.Image] = None
        self.qr_image_tk: Optional[ImageTk.PhotoImage] = None
        self.display_size = 350
        self._drag_data: Dict[str, int] = {"x": 0, "y": 0}
        
        # Color scheme
        self.colors = {
            "Blue": "#2196F3",
            "Red": "#F44336",
            "Green": "#4CAF50",
            "Purple": "#9C27B0",
            "Orange": "#FF9800",
            "Black": "#212121"
        }
        self.selected_color = tk.StringVar(value="Blue")
        
        # Theme colors
        self.theme = {
            "bg": "#FFFFFF",
            "accent": "#2196F3",
            "text": "#424242",
            "title_bar": "#F5F5F5",
            "button_hover": "#1976D2"
        }
        
        self._setup_styles()
        self.create_widgets()
        self.center_window()

    def _setup_styles(self) -> None:
        """Configure modern application styles."""
        self.style = ttk.Style()
        self.style.theme_use("clam")
        
        # Configure styles
        self.style.configure("TFrame", background=self.theme["bg"])
        self.style.configure("TitleBar.TFrame", background=self.theme["title_bar"])
        
        # Modern button style
        self.style.configure(
            "TButton",
            padding=(15, 8),
            background=self.theme["accent"],
            foreground="white",
            borderwidth=0,
            font=("Segoe UI", 10),
            relief=tk.FLAT
        )
        self.style.map(
            "TButton",
            background=[("active", self.theme["button_hover"])],
            foreground=[("active", "white")]
        )
        
        # Label styles
        self.style.configure(
            "TLabel",
            background=self.theme["bg"],
            foreground=self.theme["text"],
            font=("Segoe UI", 10)
        )
        self.style.configure(
            "Title.TLabel",
            background=self.theme["title_bar"],
            foreground=self.theme["accent"],
            font=("Segoe UI", 12, "bold")
        )
        
        # Entry style
        self.style.configure(
            "TEntry",
            padding=10,
            borderwidth=1,
            relief=tk.SOLID,
            fieldbackground=self.theme["bg"],
            font=("Segoe UI", 10)
        )
        
        # Close button style
        self.style.configure(
            "Close.TButton",
            background=self.theme["title_bar"],
            foreground=self.theme["text"],
            font=("Segoe UI", 12),
            relief=tk.FLAT,
            padding=(8, 4)
        )
        self.style.map(
            "Close.TButton",
            background=[("active", "#E53935")],
            foreground=[("active", "white")]
        )
        
        # Notebook style
        self.style.configure(
            "TNotebook",
            background=self.theme["bg"],
            borderwidth=0,
            tabmargins=[2, 5, 2, 0]
        )
        
        # Active tab style
        self.style.configure(
            "TNotebook.Tab",
            padding=(30, 10),
            background=self.theme["title_bar"],
            font=("Segoe UI", 11, "bold"),
            borderwidth=0
        )
        self.style.map(
            "TNotebook.Tab",
            background=[("selected", self.theme["accent"])],
            foreground=[("selected", "white")],
            expand=[("selected", [1, 1, 1, 0])]
        )
        
        # Inactive tab style
        self.style.configure(
            "Inactive.TNotebook.Tab",
            padding=(20, 8),
            background=self.theme["title_bar"],
            font=("Segoe UI", 10),
            borderwidth=0
        )
        self.style.map(
            "Inactive.TNotebook.Tab",
            background=[("selected", self.theme["accent"])],
            foreground=[("selected", "white")],
            expand=[("selected", [1, 1, 1, 0])]
        )

    def start_drag(self, event: tk.Event) -> None:
        """Capture the initial mouse position for dragging."""
        self._drag_data["x"] = event.x
        self._drag_data["y"] = event.y

    def on_drag(self, event: tk.Event) -> None:
        """Move the window based on mouse drag."""
        dx = event.x - self._drag_data["x"]
        dy = event.y - self._drag_data["y"]
        x = self.root.winfo_x() + dx
        y = self.root.winfo_y() + dy
        self.root.geometry(f"+{x}+{y}")

    def center_window(self) -> None:
        """Center the application window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self) -> None:
        """Create a modern, clean UI layout."""
        main_frame = ttk.Frame(self.root, style="TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title Bar
        title_bar = ttk.Frame(main_frame, style="TitleBar.TFrame", height=40)
        title_bar.pack(fill=tk.X)
        title_bar.pack_propagate(False)
        
        title_label = ttk.Label(title_bar, text="QR Code Generator & Decoder", style="Title.TLabel")
        title_label.pack(expand=True, padx=15, pady=5)
        
        close_btn = ttk.Button(title_bar, text="×", style="Close.TButton", command=self.root.quit)
        close_btn.pack(side=tk.RIGHT, padx=5)
        
        # Bind drag events
        for widget in [title_bar, title_label]:
            widget.bind("<Button-1>", self.start_drag)
            widget.bind("<B1-Motion>", self.on_drag)
        
        # Main Content with Tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, padx=25, pady=20)
        
        # Generator Tab
        generator_frame = ttk.Frame(notebook, style="TFrame")
        notebook.add(generator_frame, text="Generate QR")
        
        # Input Section
        input_frame = ttk.Frame(generator_frame, style="TFrame")
        input_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(
            input_frame,
            text="Enter URL or Text:",
            font=("Segoe UI", 10, "bold"),
            style="TLabel"
        ).pack(side=tk.LEFT, anchor="w")
        
        self.url_entry = ttk.Entry(input_frame, style="TEntry", width=35)
        self.url_entry.pack(side=tk.LEFT, padx=(10, 15), fill=tk.X, expand=True)
        
        ttk.Label(
            input_frame,
            text="Color:",
            font=("Segoe UI", 10, "bold"),
            style="TLabel"
        ).pack(side=tk.LEFT)
        
        self.color_combo = ttk.Combobox(
            input_frame,
            textvariable=self.selected_color,
            values=list(self.colors.keys()),
            state="readonly",
            width=8
        )
        self.color_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # QR Display
        self.qr_display_label = ttk.Label(
            generator_frame,
            background=self.theme["bg"],
            anchor=tk.CENTER,
            style="TLabel"
        )
        self.qr_display_label.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Buttons
        button_frame = ttk.Frame(generator_frame, style="TFrame")
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Center container for buttons
        button_container = ttk.Frame(button_frame, style="TFrame")
        button_container.pack(expand=True)
        
        self.generate_btn = ttk.Button(
            button_container,
            text="Generate QR",
            command=self.generate_qr,
            width=15
        )
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.save_btn = ttk.Button(
            button_container,
            text="Save QR",
            command=self.save_qr,
            state=tk.DISABLED,
            width=15
        )
        self.save_btn.pack(side=tk.LEFT)
        
        # Decoder Tab
        decoder_frame = ttk.Frame(notebook, style="TFrame")
        notebook.add(decoder_frame, text="Decode QR")
        
        # Decoder UI
        decoder_input_frame = ttk.Frame(decoder_frame, style="TFrame")
        decoder_input_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Center container for decoder buttons
        decoder_button_container = ttk.Frame(decoder_input_frame, style="TFrame")
        decoder_button_container.pack(expand=True)
        
        self.upload_btn = ttk.Button(
            decoder_button_container,
            text="Upload Image",
            command=self.upload_image,
            width=15
        )
        self.upload_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.decode_btn = ttk.Button(
            decoder_button_container,
            text="Decode QR",
            command=self.decode_qr,
            state=tk.DISABLED,
            width=15
        )
        self.decode_btn.pack(side=tk.LEFT)
        
        # Decoded QR Display (centered)
        decoded_image_container = ttk.Frame(decoder_frame, style="TFrame")
        decoded_image_container.pack(expand=True)
        self.decoded_image_label = ttk.Label(
            decoded_image_container,
            background=self.theme["bg"],
            anchor=tk.CENTER,
            style="TLabel"
        )
        self.decoded_image_label.pack(pady=10)
        
        # Decoded Text Display (centered and more visible)
        decoded_text_container = ttk.Frame(decoder_frame, style="TFrame")
        decoded_text_container.pack(expand=True)
        self.decoded_text = tk.Text(
            decoded_text_container,
            height=6,
            wrap=tk.WORD,
            font=("Segoe UI", 12, "bold"),
            relief=tk.SOLID,
            borderwidth=2,
            bg="#f7f7f7",
            fg="#222222"
        )
        self.decoded_text.pack(pady=(0, 20), ipadx=10, ipady=10)
        self.decoded_text.config(state=tk.DISABLED)
        
        # Bind events
        self.color_combo.bind('<<ComboboxSelected>>', lambda e: self.update_color())
        self.url_entry.bind('<Return>', lambda e: self.generate_qr())
        self.url_entry.focus()

        # Bind tab change event
        notebook.bind('<<NotebookTabChanged>>', self._on_tab_changed)
        
        # Store references to frames
        self.generator_frame = generator_frame
        self.decoder_frame = decoder_frame
        self.notebook = notebook

    def _on_tab_changed(self, event):
        """Handle tab change events to update tab styles."""
        current_tab = self.notebook.select()
        tab_id = self.notebook.index(current_tab)
        
        # Reset all tabs to inactive style
        for i in range(self.notebook.index('end')):
            self.notebook.tab(i, style='Inactive.TNotebook.Tab')
        
        # Set current tab to active style
        self.notebook.tab(tab_id, style='TNotebook.Tab')
        
        # Update frame sizes
        if tab_id == 0:  # Generator tab
            self.generator_frame.pack(fill=tk.BOTH, expand=True)
            self.decoder_frame.pack_forget()
        else:  # Decoder tab
            self.decoder_frame.pack(fill=tk.BOTH, expand=True)
            self.generator_frame.pack_forget()

    def update_color(self) -> None:
        """Update the QR code color based on selection."""
        if self.qr_image_pil:
            self.generate_qr()  # Regenerate QR code with new color

    def update_qr_display(self) -> None:
        """Updates the label with the generated QR code image."""
        if self.qr_image_pil:
            img_resized = self.qr_image_pil.resize(
                (self.display_size, self.display_size), Image.Resampling.LANCZOS
            )
            self.qr_image_tk = ImageTk.PhotoImage(img_resized)
            self.qr_display_label.configure(image=self.qr_image_tk)
            self.save_btn.configure(state=tk.NORMAL)
        else:
            self.qr_display_label.configure(image="")  # Clear image
            self.save_btn.configure(state=tk.DISABLED)

    def generate_qr(self) -> None:
        """Generates the QR code from the input data."""
        data = self.url_entry.get().strip()
        if not data:
            messagebox.showerror("Error", "Input data cannot be empty.")
            return

        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_H,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            # Ensure image is RGB for broader compatibility (e.g. with saving as JPG)
            self.qr_image_pil = qr.make_image(
                fill_color=self.colors[self.selected_color.get()], back_color="white"
            ).convert("RGB")
            self.update_qr_display()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate QR code: {str(e)}")
            self.qr_image_pil = None  # Reset on failure
            self.update_qr_display()

    def save_qr(self) -> None:
        """Saves the generated QR code to a file."""
        if not self.qr_image_pil:
            messagebox.showwarning("No QR Code", "Please generate a QR code first.")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            initialfile=f"qrcode_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("All files", "*.*"),
            ],
        )

        if file_path:
            try:
                self.qr_image_pil.save(file_path)
                messagebox.showinfo(
                    "Success", f"QR Code saved successfully to:\n{file_path}"
                )
            except Exception as e:
                messagebox.showerror("Save Error", f"Failed to save QR code: {str(e)}")

    def upload_image(self) -> None:
        """Handle image upload for QR code decoding."""
        file_path = filedialog.askopenfilename(
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                # Load and display the image
                image = Image.open(file_path)
                image.thumbnail((self.display_size, self.display_size))
                self.decoded_image_tk = ImageTk.PhotoImage(image)
                self.decoded_image_label.configure(image=self.decoded_image_tk)
                self.decode_btn.configure(state=tk.NORMAL)
                self.uploaded_image = image
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")

    def decode_qr(self) -> None:
        """Decode QR code from the uploaded image."""
        if not hasattr(self, 'uploaded_image'):
            messagebox.showwarning("No Image", "Please upload an image first.")
            return
        
        try:
            # Decode QR code
            decoded_objects = decode(self.uploaded_image)
            
            if not decoded_objects:
                messagebox.showinfo("No QR Code", "No QR code found in the image.")
                return
            
            # Display decoded text
            self.decoded_text.config(state=tk.NORMAL)
            self.decoded_text.delete(1.0, tk.END)
            for obj in decoded_objects:
                self.decoded_text.insert(tk.END, obj.data.decode('utf-8') + '\n')
            self.decoded_text.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to decode QR code: {str(e)}")


def main() -> None:
    root = tk.Tk()
    app = QRCodeGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
